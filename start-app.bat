@echo off
echo Запуск системы управления товарами...
echo.

echo Сборка проектов...
dotnet build BlazorServer
if %errorlevel% neq 0 (
    echo Ошибка при сборке серверного проекта
    pause
    exit /b 1
)

dotnet build BlazorClient
if %errorlevel% neq 0 (
    echo Ошибка при сборке клиентского проекта
    pause
    exit /b 1
)

echo.
echo Запуск серверной части...
start "BlazorServer" cmd /k "cd BlazorServer && dotnet run"

echo Ожидание запуска сервера...
timeout /t 5 /nobreak > nul

echo.
echo Запуск клиентской части...
start "BlazorClient" cmd /k "cd BlazorClient && dotnet run"

echo.
echo Приложение запущено!
echo Сервер: https://localhost:7297
echo Клиент: https://localhost:7084
echo.
echo Нажмите любую клавишу для завершения...
pause > nul
