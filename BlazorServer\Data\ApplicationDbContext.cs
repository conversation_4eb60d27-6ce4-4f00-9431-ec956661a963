using Microsoft.EntityFrameworkCore;
using BlazorServer.Models;

namespace BlazorServer.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    public DbSet<Product> Products { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Настройка модели Product
        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Price).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Category).IsRequired().HasMaxLength(50);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
        });

        // Добавляем тестовые данные
        modelBuilder.Entity<Product>().HasData(
            new Product
            {
                Id = 1,
                Name = "Ноутбук ASUS",
                Description = "Игровой ноутбук с высокой производительностью",
                Price = 85000m,
                Category = "Компьютеры",
                InStock = true,
                CreatedDate = DateTime.UtcNow
            },
            new Product
            {
                Id = 2,
                Name = "Смартфон Samsung Galaxy",
                Description = "Современный смартфон с отличной камерой",
                Price = 45000m,
                Category = "Телефоны",
                InStock = true,
                CreatedDate = DateTime.UtcNow
            },
            new Product
            {
                Id = 3,
                Name = "Наушники Sony",
                Description = "Беспроводные наушники с шумоподавлением",
                Price = 15000m,
                Category = "Аудио",
                InStock = false,
                CreatedDate = DateTime.UtcNow
            }
        );
    }
}
