# 🛍️ Система управления товарами

Полнофункциональное клиент-серверное приложение для управления товарами интернет-магазина, построенное на архитектуре Blazor WebAssembly с ASP.NET Core backend.

## 🚀 Статус проекта

✅ **ГОТОВО К ИСПОЛЬЗОВАНИЮ** - Приложение полностью функционально и готово к запуску!

## ⚡ Быстрый старт

1. **Запустите приложение:**
   ```bash
   # Автоматический запуск (рекомендуется)
   start-app.bat

   # Или вручную в двух терминалах:
   # Терминал 1: cd BlazorServer && dotnet run
   # Терминал 2: cd BlazorClient && dotnet run
   ```

2. **Откройте в браузере:**
   - 🌐 **Клиент**: https://localhost:7084
   - 🔧 **API**: https://localhost:7297

3. **Начните работу:**
   - Просмотрите список товаров
   - Добавьте новый товар
   - Попробуйте поиск и фильтрацию
   - Переключите тему оформления

## 📋 Документация

- 📖 [**Руководство пользователя**](USAGE.md) - Подробное описание всех функций
- 🏗️ [**Архитектура системы**](ARCHITECTURE.md) - Техническое описание
- 🧪 [**Тестирование API**](API_TESTING.md) - Примеры запросов к API

## Архитектура проекта

- **BlazorServer** - серверное приложение на ASP.NET Core, выступающее как API host
- **BlazorClient** - клиентское приложение на Blazor WebAssembly

## Функциональность

### Модель данных Product
- Id (int, первичный ключ)
- Name (string, название товара)
- Description (string, описание товара)
- Price (decimal, цена)
- Category (string, категория товара)
- InStock (bool, наличие на складе)
- CreatedDate (DateTime, дата создания)

### Серверная часть (BlazorServer)
- Entity Framework Core с базой данных SQLite
- ProductsController с полным CRUD API
- Валидация данных и обработка ошибок
- CORS настройки для работы с Blazor WebAssembly клиентом

### Клиентская часть (BlazorClient)
- **Страница списка товаров** (`/products`):
  - Таблица с отображением всех товаров
  - Кнопки для редактирования и удаления каждого товара
  - Кнопка для добавления нового товара
  - Поиск и фильтрация по категориям

- **Страница добавления товара** (`/products/add`):
  - Форма с полями для всех свойств товара
  - Валидация на клиентской стороне
  - Кнопки "Сохранить" и "Отмена"

- **Страница редактирования товара** (`/products/edit/{id}`):
  - Предзаполненная форма с данными товара
  - Возможность изменения всех полей
  - Кнопки "Сохранить изменения" и "Отмена"

- **Функция удаления товара**:
  - Modal диалог с подтверждением удаления
  - Отображение информации о товаре перед удалением

### Дополнительные возможности
- **Переключатель тем оформления** (светлая/темная)
- **HTTP клиент** с обработкой ошибок
- **Навигация** с breadcrumb
- **Bootstrap** стилизация
- **Индикаторы загрузки**

## Требования

- .NET 9.0 или новее
- Visual Studio 2022 или VS Code

## Установка и запуск

### 1. Клонирование репозитория
```bash
git clone <repository-url>
cd ProductManagement
```

### 2. Восстановление пакетов
```bash
dotnet restore
```

### 3. Запуск серверной части
```bash
cd BlazorServer
dotnet run
```
Сервер будет доступен по адресу: https://localhost:7297

### 4. Запуск клиентской части (в новом терминале)
```bash
cd BlazorClient
dotnet run
```
Клиент будет доступен по адресу: https://localhost:7084

### 5. Альтернативный способ запуска
Можно запустить оба проекта одновременно из корневой папки:
```bash
# Запуск сервера в фоне
start dotnet run --project BlazorServer

# Запуск клиента
dotnet run --project BlazorClient
```

## API Endpoints

- `GET /api/products` - получить все товары
- `GET /api/products/{id}` - получить товар по ID
- `POST /api/products` - создать новый товар
- `PUT /api/products/{id}` - обновить существующий товар
- `DELETE /api/products/{id}` - удалить товар по ID

## База данных

Приложение использует SQLite базу данных, которая автоматически создается при первом запуске сервера. Файл базы данных: `products.db`

### Тестовые данные
При создании базы данных автоматически добавляются тестовые товары:
- Ноутбук ASUS (Компьютеры)
- Смартфон Samsung Galaxy (Телефоны)
- Наушники Sony (Аудио)

## Технологии

- **Backend**: ASP.NET Core 9.0, Entity Framework Core, SQLite
- **Frontend**: Blazor WebAssembly, Bootstrap 5, Bootstrap Icons
- **Архитектура**: Clean Architecture, Dependency Injection
- **Валидация**: Data Annotations, Client-side validation

## Структура проекта

```
ProductManagement/
├── BlazorServer/              # ASP.NET Core API
│   ├── Controllers/           # API контроллеры
│   ├── Data/                  # DbContext и конфигурация БД
│   ├── Models/                # Модели данных
│   └── Program.cs             # Точка входа сервера
└── BlazorClient/              # Blazor WebAssembly
    ├── Pages/                 # Страницы приложения
    ├── Services/              # HTTP сервисы
    ├── Models/                # DTO модели
    ├── Layout/                # Компоненты макета
    └── Program.cs             # Точка входа клиента
```

## Возможности для развития

- Добавление аутентификации и авторизации
- Пагинация для больших списков товаров
- Загрузка изображений товаров
- Экспорт данных в Excel/PDF
- Уведомления о низком остатке товаров
- История изменений товаров
- Многоязычность интерфейса
